import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// For server-side operations
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Database Tables
export const TABLES = {
  USERS: 'users',
  MANGA: 'manga',
  CHAPTERS: 'chapters',
  PAGES: 'pages',
  GENRES: 'genres',
  MANGA_GENRES: 'manga_genres',
  BOOKMARKS: 'bookmarks',
  READING_HISTORY: 'reading_history',
  COMMENTS: 'comments',
  RATINGS: 'ratings'
} as const;
