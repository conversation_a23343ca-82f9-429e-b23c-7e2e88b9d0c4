import React from "react";
import Link from "next/link";
import { T<PERSON><PERSON>U<PERSON>, <PERSON>, <PERSON>, ArrowRight } from "lucide-react";
import Button from "@/components/common/Button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import MangaGrid from "@/components/manga/MangaGrid";
import { apiService } from "@/services/api.service";
import { Manga } from "@/types";

export default async function Home() {
  const featuredManga = await apiService.getFeaturedMangaWithFallback(6);
  const latestManga = await apiService.getLatestMangaWithFallback(6);
  const popularManga = await apiService.getPopularMangaWithFallback(6);

  return (
    <div className="container mx-auto px-4 py-8 space-y-12">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12 text-white overflow-hidden">
        <div className="relative z-10">
          <h1 className="text-4xl md:text-6xl font-bold mb-4">
            Read Manga Online
          </h1>
          <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-2xl">
            Discover thousands of manga, manhwa, and manhua titles. Read for
            free with high-quality images and fast loading.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button size="lg" variant="secondary">
              <Link href="/browse">Browse Manga</Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-blue-600"
            >
              <Link href="/latest">Latest Updates</Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-blue-600"
            >
              <Link href="/read/demo-chapter">Try Reader Demo</Link>
            </Button>
          </div>
        </div>
        <div className="absolute top-0 right-0 w-1/3 h-full opacity-10">
          <div className="w-full h-full bg-gradient-to-l from-white/20 to-transparent"></div>
        </div>
      </section>

      {/* Featured Section */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-gray-900 flex items-center">
            <Star className="mr-3 h-8 w-8 text-yellow-500" />
            Featured Manga
          </h2>
          <Link
            href="/featured"
            className="flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            View All
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
        <MangaGrid manga={featuredManga} />
      </section>

      {/* Latest Updates */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-gray-900 flex items-center">
            <Clock className="mr-3 h-8 w-8 text-green-500" />
            Latest Updates
          </h2>
          <Link
            href="/latest"
            className="flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            View All
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
        <MangaGrid manga={latestManga} />
      </section>

      {/* Popular This Week */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-gray-900 flex items-center">
            <TrendingUp className="mr-3 h-8 w-8 text-red-500" />
            Popular This Week
          </h2>
          <Link
            href="/popular"
            className="flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            View All
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
        <MangaGrid manga={popularManga} />
      </section>

      {/* Stats Section */}
      <section className="bg-white rounded-2xl p-8 shadow-sm border">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">10,000+</div>
            <div className="text-gray-600">Manga Titles</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-green-600 mb-2">500K+</div>
            <div className="text-gray-600">Chapters</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-purple-600 mb-2">1M+</div>
            <div className="text-gray-600">Active Readers</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-red-600 mb-2">24/7</div>
            <div className="text-gray-600">Updates</div>
          </div>
        </div>
      </section>
    </div>
  );
}
