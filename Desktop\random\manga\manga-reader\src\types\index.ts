// User Types
export interface User {
  id: string;
  email: string;
  username: string;
  avatar_url?: string;
  role: 'user' | 'admin';
  created_at: string;
  updated_at: string;
}

// Manga Types
export interface Manga {
  id: string;
  title: string;
  alternative_titles?: string[];
  description: string;
  cover_image: string;
  status: 'ongoing' | 'completed' | 'hiatus' | 'cancelled';
  type: 'manga' | 'manhwa' | 'manhua' | 'webtoon';
  genres: Genre[];
  authors: string[];
  artists: string[];
  year: number;
  rating: number;
  views: number;
  bookmarks: number;
  created_at: string;
  updated_at: string;
  latest_chapter?: Chapter;
}

// Chapter Types
export interface Chapter {
  id: string;
  manga_id: string;
  chapter_number: number;
  title?: string;
  pages: Page[];
  views: number;
  created_at: string;
  updated_at: string;
}

// Page Types
export interface Page {
  id: string;
  chapter_id: string;
  page_number: number;
  image_url: string;
  width?: number;
  height?: number;
}

// Genre Types
export interface Genre {
  id: string;
  name: string;
  slug: string;
}

// Bookmark Types
export interface Bookmark {
  id: string;
  user_id: string;
  manga_id: string;
  created_at: string;
}

// Reading History Types
export interface ReadingHistory {
  id: string;
  user_id: string;
  manga_id: string;
  chapter_id: string;
  page_number: number;
  created_at: string;
  updated_at: string;
}

// Comment Types
export interface Comment {
  id: string;
  user_id: string;
  manga_id: string;
  chapter_id?: string;
  content: string;
  rating?: number;
  created_at: string;
  updated_at: string;
  user: User;
}

// Search Types
export interface SearchFilters {
  genres?: string[];
  status?: string[];
  type?: string[];
  year?: number[];
  rating?: number;
  sortBy?: 'title' | 'rating' | 'views' | 'updated_at' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Reader Types
export interface ReaderSettings {
  readingMode: 'single' | 'double' | 'webtoon';
  readingDirection: 'ltr' | 'rtl';
  fitMode: 'width' | 'height' | 'original';
  backgroundColor: string;
  autoNext: boolean;
}
