{"name": "manga-reader", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@next-auth/supabase-adapter": "^0.2.1", "@supabase/supabase-js": "^2.50.0", "framer-motion": "^12.18.1", "lucide-react": "^0.516.0", "next": "15.3.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.16.0", "sharp": "^0.34.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}