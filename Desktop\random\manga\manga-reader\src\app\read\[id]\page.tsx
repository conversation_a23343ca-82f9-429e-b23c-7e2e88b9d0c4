import React from "react";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { apiService } from "@/services/api.service";
import MangaReader from "@/components/reader/MangaReader";

interface ReaderPageProps {
  params: { id: string };
}

export async function generateMetadata({
  params,
}: ReaderPageProps): Promise<Metadata> {
  try {
    // For now, we'll use a generic title since we need the chapter data
    return {
      title: "Reading Chapter - MangaReader",
      description: "Read manga online with our advanced reader",
    };
  } catch (error) {
    return {
      title: "Chapter Not Found - MangaReader",
    };
  }
}

export default async function ReaderPage({ params }: ReaderPageProps) {
  try {
    const { id } = await params;

    // Try to get chapter pages
    let pages;
    try {
      pages = await apiService.getChapterPages(id);
    } catch (error) {
      console.error("Error loading chapter pages:", error);
      // Create mock pages for demonstration
      pages = Array.from({ length: 10 }, (_, i) => ({
        id: `page-${i + 1}`,
        chapter_id: id,
        page_number: i + 1,
        image_url: `/api/placeholder?width=800&height=1200&bg=6366F1&color=FFFFFF&text=Page+${
          i + 1
        }`,
        width: 800,
        height: 1200,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));
    }

    if (!pages || pages.length === 0) {
      // Create fallback pages
      pages = Array.from({ length: 5 }, (_, i) => ({
        id: `fallback-page-${i + 1}`,
        chapter_id: id,
        page_number: i + 1,
        image_url: `/api/placeholder?width=800&height=1200&bg=DC2626&color=FFFFFF&text=Demo+Page+${
          i + 1
        }`,
        width: 800,
        height: 1200,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));
    }

    // For now, we'll create mock data for the chapter and manga
    // In a real implementation, you'd get this from your database or API
    const currentChapter = {
      id: id,
      manga_id: "demo-manga-id",
      chapter_number: 1,
      title: "Demo Chapter - Reader Interface",
      pages,
      views: 1250,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const manga = {
      id: "demo-manga-id",
      title: "Demo Manga Reader",
      description:
        "A demonstration of the professional manga reader interface with multiple reading modes, zoom controls, and keyboard navigation.",
      cover_image:
        "/api/placeholder?width=300&height=400&bg=7C3AED&color=FFFFFF&text=Demo+Manga",
      status: "ongoing" as const,
      type: "manga" as const,
      genres: [],
      authors: ["Demo Author"],
      artists: ["Demo Artist"],
      year: 2024,
      rating: 8.5,
      views: 15000,
      bookmarks: 500,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const allChapters = [currentChapter]; // In real implementation, get all chapters

    return (
      <MangaReader
        pages={pages}
        currentChapter={currentChapter}
        manga={manga}
        allChapters={allChapters}
      />
    );
  } catch (error) {
    console.error("Error loading chapter:", error);
    notFound();
  }
}
